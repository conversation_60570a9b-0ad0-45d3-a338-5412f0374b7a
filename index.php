<?php
session_start();

// Handle cart operations first (before any output)
if (isset($_POST['delc'])) {
    // Database connection for cart operations
    $servername = "localhost";
    $username   = "root";
    $password   = "";
    $conn = new mysqli($servername, $username, $password);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    $conn->query("USE bookstore");

    $sql = "DELETE FROM cart";
    $conn->query($sql);
    $conn->close();
    header("Location: index.php");
    exit();
}

// Database connection
$servername = "localhost";
$username   = "root";
$password   = "";
$conn = new mysqli($servername, $username, $password);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
$conn->query("USE bookstore");

// Handle add to cart
if (isset($_POST['ac'])) {
    $sql    = "SELECT * FROM book WHERE BookID = '" . $conn->real_escape_string($_POST['ac']) . "'";
    $result = $conn->query($sql);

    if ($row = $result->fetch_assoc()) {
        $bookID   = $row['BookID'];
        $quantity = (int) $_POST['quantity'];
        $price    = $row['Price'];

        $sql = "INSERT INTO cart(BookID, Quantity, Price, TotalPrice)
                VALUES('$bookID', $quantity, $price, $price * $quantity)";
        $conn->query($sql);
    }
}

// Handle empty cart
if (isset($_POST['delc'])) {
    $conn->query("DELETE FROM cart");
}

// Get books
$sql    = "SELECT * FROM book";
$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookstore - Your Online Book Destination</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <div class="header-content">
            <a href="index.php" class="logo-link">
                <img src="image/logo.png" alt="Bookstore Logo" class="logo">
            </a>
            <div class="hf">
                <?php if (isset($_SESSION['id'])): ?>
                    <span class="welcome-text">Welcome, <?php echo htmlspecialchars(isset($_SESSION['username']) ? $_SESSION['username'] : 'User'); ?>!</span>
                    <a href="edituser.php" class="hi">
                        <i class="fas fa-user-edit"></i> Edit Profile
                    </a>
                    <a href="logout.php" class="hi">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                <?php else: ?>
                    <a href="register.php" class="hi">
                        <i class="fas fa-user-plus"></i> Register
                    </a>
                    <a href="login.php" class="hi">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="bookstore-header">
                <h1><i class="fas fa-book"></i> Our Book Collection</h1>
                <p>Discover amazing books from various genres</p>
            </div>

            <div class="books-grid">
                <?php while ($row = $result->fetch_assoc()): ?>
                    <div class="book-card">
                        <div class="book-image">
                            <img src="<?php echo htmlspecialchars($row["Image"]); ?>" alt="<?php echo htmlspecialchars($row["BookTitle"]); ?>">
                            <div class="book-overlay">
                                <span class="book-type"><?php echo htmlspecialchars($row["Type"]); ?></span>
                            </div>
                        </div>
                        <div class="book-info">
                            <h3 class="book-title"><?php echo htmlspecialchars($row["BookTitle"]); ?></h3>
                            <p class="book-author"><i class="fas fa-user"></i> <?php echo htmlspecialchars($row["Author"]); ?></p>
                            <p class="book-isbn"><i class="fas fa-barcode"></i> <?php echo htmlspecialchars($row["ISBN"]); ?></p>
                            <div class="book-price">
                                <span class="price">RM <?php echo number_format($row["Price"], 2); ?></span>
                            </div>
                            <form action="" method="post" class="add-to-cart-form">
                                <div class="quantity-selector">
                                    <label for="quantity_<?php echo $row['BookID']; ?>">Quantity:</label>
                                    <input type="number" id="quantity_<?php echo $row['BookID']; ?>" name="quantity" value="1" min="1" max="10">
                                </div>
                                <input type="hidden" value="<?php echo $row['BookID']; ?>" name="ac">
                                <button type="submit" class="add-to-cart-btn">
                                    <i class="fas fa-cart-plus"></i> Add to Cart
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>

            <!-- Shopping Cart -->
            <div class="cart-section">
                <div class="cart-header">
                    <h2><i class="fas fa-shopping-cart"></i> Shopping Cart</h2>
                    <form action="" method="post" class="empty-cart-form">
                        <input type="hidden" name="delc">
                        <button type="submit" class="empty-cart-btn">
                            <i class="fas fa-trash"></i> Empty Cart
                        </button>
                    </form>
                </div>

                <div class="cart-items">
                    <?php
                    // Cart display
                    $sql = "SELECT book.BookTitle, book.Image, cart.Price, cart.Quantity, cart.TotalPrice
                           FROM book, cart
                           WHERE book.BookID = cart.BookID";
                    $result = $conn->query($sql);
                    $total = 0;
                    $itemCount = 0;

                    if ($result->num_rows > 0):
                        while ($row = $result->fetch_assoc()):
                            $total += $row['TotalPrice'];
                            $itemCount++;
                    ?>
                        <div class="cart-item">
                            <img src="<?php echo htmlspecialchars($row["Image"]); ?>" alt="<?php echo htmlspecialchars($row["BookTitle"]); ?>" class="cart-item-image">
                            <div class="cart-item-details">
                                <h4><?php echo htmlspecialchars($row['BookTitle']); ?></h4>
                                <p class="cart-item-price">RM <?php echo number_format($row['Price'], 2); ?></p>
                                <p class="cart-item-quantity">Qty: <?php echo $row['Quantity']; ?></p>
                                <p class="cart-item-total">Total: RM <?php echo number_format($row['TotalPrice'], 2); ?></p>
                            </div>
                        </div>
                    <?php
                        endwhile;
                    else:
                    ?>
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart"></i>
                            <p>Your cart is empty</p>
                            <p>Add some books to get started!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($itemCount > 0): ?>
                <div class="cart-summary">
                    <div class="cart-total">
                        <strong>Total: RM <?php echo number_format($total, 2); ?></strong>
                    </div>
                    <form action="checkout.php" method="post">
                        <button type="submit" name="checkout" class="checkout-btn">
                            <i class="fas fa-credit-card"></i> Checkout (<?php echo $itemCount; ?> items)
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 Bookstore. All rights reserved.</p>
        </div>
    </footer>

    <?php
    $conn->close();
    ?>
</body>
</html>
