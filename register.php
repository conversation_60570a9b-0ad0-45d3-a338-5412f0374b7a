<?php
/**
 * User Registration Handler
 * Secure registration with proper validation and error handling
 */

session_start();

// Initialize variables
$nameErr = $emailErr = $genderErr = $addressErr = $icErr = $contactErr = $usernameErr = $passwordErr = "";
$name = $email = $gender = $address = $ic = $contact = $uname = $upassword = "";
$registrationSuccess = false;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize and validate input
    $name = trim($_POST["name"] ?? '');
    $uname = trim($_POST["uname"] ?? '');
    $upassword = trim($_POST["upassword"] ?? '');
    $ic = trim($_POST["ic"] ?? '');
    $email = trim($_POST["email"] ?? '');
    $contact = trim($_POST["contact"] ?? '');
    $gender = $_POST["gender"] ?? '';
    $address = trim($_POST["address"] ?? '');
    
    $isValid = true;
    
    // Validate name
    if (empty($name)) {
        $nameErr = "Please enter your name";
        $isValid = false;
    } elseif (!preg_match("/^[a-zA-Z ]*$/", $name)) {
        $nameErr = "Only letters and white space allowed";
        $isValid = false;
    }
    
    // Validate username
    if (empty($uname)) {
        $usernameErr = "Please enter your username";
        $isValid = false;
    } elseif (strlen($uname) < 3) {
        $usernameErr = "Username must be at least 3 characters long";
        $isValid = false;
    } elseif (!preg_match("/^[a-zA-Z0-9_]*$/", $uname)) {
        $usernameErr = "Username can only contain letters, numbers, and underscores";
        $isValid = false;
    }
    
    // Validate password
    if (empty($upassword)) {
        $passwordErr = "Please enter your password";
        $isValid = false;
    } elseif (strlen($upassword) < 6) {
        $passwordErr = "Password must be at least 6 characters long";
        $isValid = false;
    }
    
    // Validate IC number
    if (empty($ic)) {
        $icErr = "Please enter your IC number";
        $isValid = false;
    } elseif (!preg_match("/^[0-9]{6}-[0-9]{2}-[0-9]{4}$/", $ic)) {
        $icErr = "Please enter a valid IC number (format: xxxxxx-xx-xxxx)";
        $isValid = false;
    }
    
    // Validate email
    if (empty($email)) {
        $emailErr = "Please enter your email address";
        $isValid = false;
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $emailErr = "Invalid email format";
        $isValid = false;
    }
    
    // Validate contact
    if (empty($contact)) {
        $contactErr = "Please enter your phone number";
        $isValid = false;
    } elseif (!preg_match("/^[0-9]{3}-[0-9]{7,8}$/", $contact)) {
        $contactErr = "Please enter a valid phone number (format: 012-3456789)";
        $isValid = false;
    }
    
    // Validate gender
    if (empty($gender)) {
        $genderErr = "Please select your gender";
        $isValid = false;
    } elseif (!in_array($gender, ['Male', 'Female'])) {
        $genderErr = "Please select a valid gender";
        $isValid = false;
    }
    
    // Validate address
    if (empty($address)) {
        $addressErr = "Please enter your address";
        $isValid = false;
    }
    
    // If all validations pass, process registration
    if ($isValid) {
        try {
            include "connectDB.php";
            
            // Check if username already exists
            $checkUserSql = "SELECT UserID FROM Users WHERE UserName = :username";
            $checkStmt = $pdo->prepare($checkUserSql);
            $checkStmt->execute([':username' => $uname]);
            
            if ($checkStmt->rowCount() > 0) {
                $usernameErr = "Username already exists. Please choose a different username.";
            } else {
                // Check if email already exists
                $checkEmailSql = "SELECT CustomerID FROM Customer WHERE CustomerEmail = :email";
                $checkEmailStmt = $pdo->prepare($checkEmailSql);
                $checkEmailStmt->execute([':email' => $email]);
                
                if ($checkEmailStmt->rowCount() > 0) {
                    $emailErr = "Email already registered. Please use a different email.";
                } else {
                    // Begin transaction
                    $pdo->beginTransaction();
                    
                    // Insert user
                    $userSql = "INSERT INTO Users (UserName, Password) VALUES (:username, :password)";
                    $userStmt = $pdo->prepare($userSql);
                    $userStmt->execute([
                        ':username' => $uname,
                        ':password' => $upassword // Note: In production, hash the password
                    ]);
                    
                    $userId = $pdo->lastInsertId();
                    
                    // Insert customer
                    $customerSql = "INSERT INTO Customer (CustomerName, CustomerPhone, CustomerIC, CustomerEmail, CustomerAddress, CustomerGender, UserID) 
                                   VALUES (:name, :phone, :ic, :email, :address, :gender, :userid)";
                    $customerStmt = $pdo->prepare($customerSql);
                    $customerStmt->execute([
                        ':name' => $name,
                        ':phone' => $contact,
                        ':ic' => $ic,
                        ':email' => $email,
                        ':address' => $address,
                        ':gender' => $gender,
                        ':userid' => $userId
                    ]);
                    
                    // Commit transaction
                    $pdo->commit();
                    
                    // Set success flag and redirect
                    $registrationSuccess = true;
                    header("Location: login.php?success=1");
                    exit();
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }
            error_log("Registration error: " . $e->getMessage());
            $nameErr = "Registration failed. Please try again.";
        } catch (Exception $e) {
            error_log("General registration error: " . $e->getMessage());
            $nameErr = "Registration failed. Please try again.";
        }
    }
}

function test_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Bookstore</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <a href="index.php" class="logo-link">
                <img src="image/logo.png" alt="Bookstore Logo" class="logo">
            </a>
        </div>
    </header>
    
    <main class="main-content">
        <div class="container">
            <div class="register-form-wrapper">
                <h1 class="form-title"><i class="fas fa-user-plus"></i> Create Account</h1>
                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" class="register-form">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name"><i class="fas fa-user"></i> Full Name:</label>
                            <input type="text" id="name" name="name" placeholder="Enter your full name" value="<?php echo htmlspecialchars($name); ?>">
                            <span class="error-text"><?php echo $nameErr;?></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="uname"><i class="fas fa-at"></i> Username:</label>
                            <input type="text" id="uname" name="uname" placeholder="Choose a username" value="<?php echo htmlspecialchars($uname); ?>">
                            <span class="error-text"><?php echo $usernameErr;?></span>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="upassword"><i class="fas fa-lock"></i> Password:</label>
                            <input type="password" id="upassword" name="upassword" placeholder="Create a password">
                            <span class="error-text"><?php echo $passwordErr;?></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="ic"><i class="fas fa-id-card"></i> IC Number:</label>
                            <input type="text" id="ic" name="ic" placeholder="xxxxxx-xx-xxxx" value="<?php echo htmlspecialchars($ic); ?>">
                            <span class="error-text"><?php echo $icErr;?></span>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email"><i class="fas fa-envelope"></i> Email:</label>
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" value="<?php echo htmlspecialchars($email); ?>">
                            <span class="error-text"><?php echo $emailErr;?></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact"><i class="fas fa-phone"></i> Mobile Number:</label>
                            <input type="tel" id="contact" name="contact" placeholder="012-3456789" value="<?php echo htmlspecialchars($contact); ?>">
                            <span class="error-text"><?php echo $contactErr;?></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="gender-label"><i class="fas fa-venus-mars"></i> Gender:</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="gender" value="Male" <?php if (isset($gender) && $gender == "Male") echo "checked";?>>
                                <span class="radio-custom"></span>
                                Male
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="gender" value="Female" <?php if (isset($gender) && $gender == "Female") echo "checked";?>>
                                <span class="radio-custom"></span>
                                Female
                            </label>
                        </div>
                        <span class="error-text"><?php echo $genderErr;?></span>
                    </div>

                    <div class="form-group">
                        <label for="address"><i class="fas fa-map-marker-alt"></i> Address:</label>
                        <textarea id="address" name="address" rows="4" placeholder="Enter your full address" class="address-textarea"><?php echo htmlspecialchars($address); ?></textarea>
                        <span class="error-text"><?php echo $addressErr;?></span>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="submitButton" class="button primary">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                        <button type="button" class="button secondary" onclick="window.location='index.php';">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 Bookstore. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
