<?php
/**
 * Login Authentication Handler
 * Secure login processing with prepared statements
 */

session_start();

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: login.php?errcode=2");
    exit();
}

// Validate input
if (!isset($_POST['username']) || !isset($_POST['pwd']) ||
    empty(trim($_POST['username'])) || empty(trim($_POST['pwd']))) {
    header("Location: login.php?errcode=1");
    exit();
}

$username = trim($_POST['username']);
$password = trim($_POST['pwd']);

try {
    include "connectDB.php";

    // Use prepared statement to prevent SQL injection
    $sql = "SELECT UserID, UserName FROM Users WHERE UserName = :username AND Password = :password";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':username' => $username,
        ':password' => $password
    ]);

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Set session variables
        $_SESSION['id'] = $user['UserID'];
        $_SESSION['username'] = $user['UserName'];
        $_SESSION['login_time'] = time();

        // Regenerate session ID for security
        session_regenerate_id(true);

        header("Location: index.php");
        exit();
    } else {
        // Invalid credentials
        header("Location: login.php?errcode=1");
        exit();
    }

} catch (PDOException $e) {
    error_log("Login error: " . $e->getMessage());
    header("Location: login.php?errcode=1");
    exit();
} catch (Exception $e) {
    error_log("General login error: " . $e->getMessage());
    header("Location: login.php?errcode=1");
    exit();
}
?>