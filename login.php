<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Bookstore</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="header-content">
            <a href="index.php" class="logo-link">
                <img src="image/logo.png" alt="Bookstore Logo" class="logo">
            </a>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="login-form-wrapper">
                <h1 class="form-title"><i class="fas fa-sign-in-alt"></i> Login</h1>
                <form action="checklogin.php" method="post" class="login-form">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="pwd" required>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="button primary">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                        <button type="button" class="button secondary" onclick="window.location='index.php';">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php
        if (isset($_GET['success'])) {
            echo '<div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    Registration successful! Please login with your credentials.
                  </div>';
        }

        if (isset($_GET['errcode'])) {
            $errorMessages = [
                1 => 'Invalid username or password. Please try again.',
                2 => 'Please login to continue.'
            ];

            $errcode = intval($_GET['errcode']);
            if (array_key_exists($errcode, $errorMessages)) {
                echo '<div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        ' . htmlspecialchars($errorMessages[$errcode]) . '
                      </div>';
            }
        }
        ?>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 Bookstore. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
