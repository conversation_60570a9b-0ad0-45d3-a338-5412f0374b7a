<?php
/**
 * Database Connection Test Script
 * Run this file to test if your database connection is working properly
 */

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Database Connection Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }";
echo ".success { color: #27ae60; background: #d5f4e6; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #ebf3fd; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
echo "th { background-color: #f2f2f2; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Database Connection Test</h1>";

// Test 1: Check if connectDB.php exists
echo "<h2>Test 1: Check Configuration File</h2>";
if (file_exists('connectDB.php')) {
    echo "<div class='success'>✅ connectDB.php file found</div>";
} else {
    echo "<div class='error'>❌ connectDB.php file not found</div>";
    echo "</body></html>";
    exit();
}

// Test 2: Include and test database connection
echo "<h2>Test 2: Database Connection</h2>";
try {
    include 'connectDB.php';
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Test 3: Check if required tables exist
    echo "<h2>Test 3: Database Tables</h2>";
    $tables = ['Users', 'Customer', 'Book', 'Cart'];
    $tableResults = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            $tableResults[$table] = $count;
            echo "<div class='success'>✅ Table '$table' exists with $count records</div>";
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Table '$table' not found or error: " . $e->getMessage() . "</div>";
        }
    }
    
    // Test 4: Display sample data
    if (isset($tableResults['Book']) && $tableResults['Book'] > 0) {
        echo "<h2>Test 4: Sample Book Data</h2>";
        try {
            $stmt = $pdo->query("SELECT BookID, BookTitle, Author, Price FROM Book LIMIT 5");
            $books = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Book ID</th><th>Title</th><th>Author</th><th>Price</th></tr>";
            foreach ($books as $book) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($book['BookID']) . "</td>";
                echo "<td>" . htmlspecialchars($book['BookTitle']) . "</td>";
                echo "<td>" . htmlspecialchars($book['Author']) . "</td>";
                echo "<td>RM " . number_format($book['Price'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<div class='success'>✅ Sample book data loaded successfully</div>";
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Error loading book data: " . $e->getMessage() . "</div>";
        }
    }
    
    // Test 5: Check PHP version and extensions
    echo "<h2>Test 5: PHP Environment</h2>";
    echo "<div class='info'>📋 PHP Version: " . PHP_VERSION . "</div>";
    
    $extensions = ['pdo', 'pdo_mysql', 'mysqli'];
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<div class='success'>✅ Extension '$ext' is loaded</div>";
        } else {
            echo "<div class='error'>❌ Extension '$ext' is not loaded</div>";
        }
    }
    
    echo "<h2>🎉 Connection Test Complete</h2>";
    echo "<div class='success'>";
    echo "<strong>Your bookstore application should work properly!</strong><br>";
    echo "You can now access:<br>";
    echo "• <a href='index.php'>Main Bookstore Page</a><br>";
    echo "• <a href='register.php'>User Registration</a><br>";
    echo "• <a href='login.php'>User Login</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
    echo "<div class='info'>";
    echo "<strong>Troubleshooting Tips:</strong><br>";
    echo "1. Make sure MySQL/MariaDB is running<br>";
    echo "2. Check database credentials in connectDB.php<br>";
    echo "3. Ensure 'bookstore' database exists<br>";
    echo "4. Run the database.sql file to create tables<br>";
    echo "5. Check if PHP has PDO and MySQL extensions enabled";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ General error: " . $e->getMessage() . "</div>";
}

echo "</body>";
echo "</html>";
?>
