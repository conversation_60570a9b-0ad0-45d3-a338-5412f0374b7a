<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Bookstore</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #27ae60; background: #d5f4e6; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #f39c12; background: #fef9e7; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { color: #3498db; background: #ebf3fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .step { background: #f0f0f0; padding: 15px; margin: 15px 0; border-left: 4px solid #3498db; }
    </style>
</head>
<body>
    <h1>🔧 Bookstore Database Setup</h1>
    
    <?php
    // Database configuration
    $db_host = 'localhost';
    $db_port = '3306';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'bookstore';
    
    echo "<h2>Step 1: Testing MySQL Connection</h2>";
    
    // Test basic MySQL connection (without database)
    try {
        $dsn = "mysql:host=" . $db_host . ";port=" . $db_port;
        $pdo = new PDO($dsn, $db_user, $db_pass);
        echo "<div class='success'>✅ MySQL server connection successful!</div>";
        
        // Check if database exists
        echo "<h2>Step 2: Checking if 'bookstore' database exists</h2>";
        $stmt = $pdo->query("SHOW DATABASES LIKE 'bookstore'");
        $database_exists = $stmt->rowCount() > 0;
        
        if ($database_exists) {
            echo "<div class='success'>✅ Database 'bookstore' already exists!</div>";
        } else {
            echo "<div class='warning'>⚠️ Database 'bookstore' does not exist.</div>";
            echo "<div class='info'>Don't worry! I'll help you create it.</div>";
        }
        
        // If database doesn't exist, create it
        if (!$database_exists) {
            echo "<h2>Step 3: Creating 'bookstore' database</h2>";
            try {
                $pdo->exec("CREATE DATABASE bookstore");
                echo "<div class='success'>✅ Database 'bookstore' created successfully!</div>";
                $database_exists = true;
            } catch (PDOException $e) {
                echo "<div class='error'>❌ Failed to create database: " . $e->getMessage() . "</div>";
            }
        }
        
        // Test connection to bookstore database
        if ($database_exists) {
            echo "<h2>Step 4: Testing connection to 'bookstore' database</h2>";
            try {
                $dsn_with_db = "mysql:host=" . $db_host . ";port=" . $db_port . ";dbname=" . $db_name;
                $pdo_with_db = new PDO($dsn_with_db, $db_user, $db_pass);
                echo "<div class='success'>✅ Connection to 'bookstore' database successful!</div>";
                
                // Check if tables exist
                echo "<h2>Step 5: Checking database tables</h2>";
                $tables = array('Users', 'Customer', 'Book', 'Cart');
                $missing_tables = array();
                
                foreach ($tables as $table) {
                    $stmt = $pdo_with_db->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo "<div class='success'>✅ Table '$table' exists</div>";
                    } else {
                        echo "<div class='warning'>⚠️ Table '$table' is missing</div>";
                        $missing_tables[] = $table;
                    }
                }
                
                if (!empty($missing_tables)) {
                    echo "<div class='step'>";
                    echo "<h3>📋 Next Steps:</h3>";
                    echo "<p>You need to create the missing tables. Here's how:</p>";
                    echo "<ol>";
                    echo "<li>Open phpMyAdmin in your browser</li>";
                    echo "<li>Select the 'bookstore' database from the left sidebar</li>";
                    echo "<li>Click on the 'Import' tab</li>";
                    echo "<li>Click 'Choose File' and select the 'database.sql' file from your project folder</li>";
                    echo "<li>Click 'Go' to import the tables and sample data</li>";
                    echo "</ol>";
                    echo "</div>";
                    
                    echo "<div class='info'>";
                    echo "<strong>Alternative:</strong> If you don't have the database.sql file, I can create the tables for you.";
                    echo "<br><a href='?create_tables=1' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>Create Tables Now</a>";
                    echo "</div>";
                } else {
                    echo "<div class='success'>";
                    echo "<h3>🎉 Setup Complete!</h3>";
                    echo "<p>Your database is ready! You can now:</p>";
                    echo "<ul>";
                    echo "<li><a href='index.php'>Visit your bookstore website</a></li>";
                    echo "<li><a href='register.php'>Register a new account</a></li>";
                    echo "<li><a href='login.php'>Login to existing account</a></li>";
                    echo "</ul>";
                    echo "</div>";
                }
                
            } catch (PDOException $e) {
                echo "<div class='error'>❌ Failed to connect to 'bookstore' database: " . $e->getMessage() . "</div>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<div class='error'>❌ MySQL connection failed: " . $e->getMessage() . "</div>";
        echo "<div class='step'>";
        echo "<h3>🔧 Troubleshooting:</h3>";
        echo "<ul>";
        echo "<li>Make sure XAMPP/WAMP is running</li>";
        echo "<li>Check that MySQL service is started</li>";
        echo "<li>Verify that port 3306 is not blocked</li>";
        echo "<li>Try accessing phpMyAdmin at <a href='http://localhost/phpmyadmin'>http://localhost/phpmyadmin</a></li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Handle table creation
    if (isset($_GET['create_tables']) && $_GET['create_tables'] == '1') {
        echo "<h2>Step 6: Creating Database Tables</h2>";
        
        try {
            $dsn_with_db = "mysql:host=" . $db_host . ";port=" . $db_port . ";dbname=" . $db_name;
            $pdo_with_db = new PDO($dsn_with_db, $db_user, $db_pass);
            
            // Create tables
            $sql_commands = array(
                "CREATE TABLE IF NOT EXISTS Users (
                    UserID int NOT NULL AUTO_INCREMENT,
                    UserName varchar(128),
                    Password varchar(16),
                    PRIMARY KEY (UserID)
                )",
                
                "CREATE TABLE IF NOT EXISTS Customer (
                    CustomerID int NOT NULL AUTO_INCREMENT,
                    CustomerName varchar(128),
                    CustomerPhone varchar(12),
                    CustomerIC varchar(14),
                    CustomerEmail varchar(200),
                    CustomerAddress varchar(200),
                    CustomerGender varchar(10),
                    UserID int,
                    PRIMARY KEY (CustomerID),
                    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL ON UPDATE CASCADE
                )",
                
                "CREATE TABLE IF NOT EXISTS Book (
                    BookID varchar(50),
                    BookTitle varchar(200),
                    ISBN varchar(20),
                    Price double(12,2),
                    Author varchar(128),
                    Type varchar(128),
                    Image varchar(128),
                    PRIMARY KEY (BookID)
                )",
                
                "CREATE TABLE IF NOT EXISTS Cart (
                    CartID int NOT NULL AUTO_INCREMENT,
                    BookID varchar(50),
                    Quantity int,
                    Price double(12,2),
                    TotalPrice double(12,2),
                    CustomerID int,
                    PRIMARY KEY (CartID)
                )",
                
                "CREATE TABLE IF NOT EXISTS `Order` (
                    OrderID int NOT NULL AUTO_INCREMENT,
                    CustomerID int,
                    BookID varchar(50),
                    DatePurchase datetime,
                    Quantity int,
                    TotalPrice double(12,2),
                    Status varchar(1),
                    PRIMARY KEY (OrderID)
                )"
            );
            
            foreach ($sql_commands as $sql) {
                $pdo_with_db->exec($sql);
            }
            
            echo "<div class='success'>✅ All tables created successfully!</div>";
            
            // Insert sample books
            $sample_books = array(
                "INSERT IGNORE INTO Book (BookID, BookTitle, ISBN, Price, Author, Type, Image) VALUES 
                ('B-001', 'Lonely Planet Australia (Travel Guide)', '***********-1', 136.00, 'Lonely Planet', 'Travel', 'image/travel.jpg')",
                
                "INSERT IGNORE INTO Book (BookID, BookTitle, ISBN, Price, Author, Type, Image) VALUES 
                ('B-002', 'Crew Resource Management, Second Edition', '***********-2', 599.00, 'Barbara Kanki', 'Technical', 'image/technical.jpg')",
                
                "INSERT IGNORE INTO Book (BookID, BookTitle, ISBN, Price, Author, Type, Image) VALUES 
                ('B-003', 'CCNA Routing and Switching 200-125 Official Cert Guide Library', '***********-3', 329.00, 'Cisco Press', 'Technology', 'image/technology.jpg')",
                
                "INSERT IGNORE INTO Book (BookID, BookTitle, ISBN, Price, Author, Type, Image) VALUES 
                ('B-004', 'Easy Vegetarian Slow Cooker Cookbook', '***********-4', 75.90, 'Rockridge Press', 'Food', 'image/food.jpg')"
            );
            
            foreach ($sample_books as $sql) {
                $pdo_with_db->exec($sql);
            }
            
            echo "<div class='success'>✅ Sample books added successfully!</div>";
            echo "<div class='success'>";
            echo "<h3>🎉 Complete Setup Finished!</h3>";
            echo "<p>Your bookstore is now ready to use!</p>";
            echo "<p><a href='index.php' style='background: #27ae60; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Visit Your Bookstore</a></p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>❌ Failed to create tables: " . $e->getMessage() . "</div>";
        }
    }
    ?>
    
    <div class="step">
        <h3>📝 Current Configuration:</h3>
        <div class="code">
            Host: <?php echo $db_host; ?><br>
            Port: <?php echo $db_port; ?><br>
            Username: <?php echo $db_user; ?><br>
            Password: <?php echo empty($db_pass) ? '(empty)' : '(set)'; ?><br>
            Database: <?php echo $db_name; ?>
        </div>
    </div>
    
</body>
</html>
