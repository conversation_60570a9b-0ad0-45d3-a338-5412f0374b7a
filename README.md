# 📚 Modern PHP Bookstore Website

A beautiful, responsive bookstore website built with PHP, MySQL, and modern CSS. Features user authentication, book browsing, shopping cart functionality, and a clean, modern interface.

## ✨ Features

- **Modern UI/UX Design**: Clean, responsive design with smooth animations
- **User Authentication**: Secure login and registration system
- **Book Catalog**: Browse books with detailed information and images
- **Shopping Cart**: Add books to cart with quantity selection
- **User Profile Management**: Edit user profile and account details
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Security**: Protected against SQL injection with prepared statements
- **Form Validation**: Client and server-side validation with helpful error messages

## 🚀 Quick Start

### Prerequisites

- **Web Server**: Apache, Nginx, or PHP built-in server
- **PHP**: Version 7.4 or higher
- **MySQL**: Version 5.7 or higher
- **Web Browser**: Modern browser with JavaScript enabled

### Installation Steps

1. **Clone or Download the Project**
   ```bash
   git clone <repository-url>
   cd bookstore
   ```

2. **Set Up Database**
   - Create a new MySQL database named `bookstore`
   - Import the database structure and sample data:
   ```sql
   mysql -u root -p bookstore < database.sql
   ```

3. **Configure Database Connection**
   - Open `connectDB.php`
   - Update database credentials if needed:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_PORT', '3306');
   define('DB_NAME', 'bookstore');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

4. **Start the Web Server**
   
   **Option A: Using PHP Built-in Server**
   ```bash
   php -S localhost:8000
   ```
   
   **Option B: Using XAMPP/WAMP**
   - Copy the project folder to `htdocs` (XAMPP) or `www` (WAMP)
   - Start Apache and MySQL services
   - Access via `http://localhost/bookstore`

5. **Access the Website**
   - Open your browser and go to `http://localhost:8000` (or your configured URL)
   - Register a new account or use the sample data

## 📁 Project Structure

```
bookstore/
├── index.php              # Main page with book catalog
├── login.php              # User login page
├── register.php           # User registration page
├── checklogin.php         # Login authentication handler
├── edituser.php           # User profile editing
├── logout.php             # Logout handler
├── checkout.php           # Order checkout page
├── connectDB.php          # Database connection configuration
├── style.css              # Modern CSS styles
├── database.sql           # Database structure and sample data
├── image/                 # Book images and assets
│   ├── logo.png
│   ├── travel.jpg
│   ├── technical.jpg
│   ├── technology.jpg
│   └── food.jpg
└── README.md              # This file
```

## 🗄️ Database Schema

The application uses the following main tables:

- **Users**: User authentication data
- **Customer**: Customer profile information
- **Book**: Book catalog with details and images
- **Cart**: Shopping cart items
- **Order**: Order history and details

## 🎨 Design Features

### Modern UI Components
- **Gradient Backgrounds**: Beautiful gradient overlays
- **Card-based Layout**: Clean book cards with hover effects
- **Responsive Grid**: Adaptive layout for all screen sizes
- **Modern Forms**: Styled forms with validation feedback
- **Icon Integration**: Font Awesome icons throughout
- **Smooth Animations**: CSS transitions and hover effects

### Color Scheme
- **Primary**: #2c3e50 (Dark Blue-Gray)
- **Secondary**: #3498db (Blue)
- **Accent**: #e74c3c (Red)
- **Success**: #27ae60 (Green)
- **Warning**: #f39c12 (Orange)

## 🔧 Customization

### Adding New Books
1. Add book images to the `image/` folder
2. Insert book data into the `Book` table via phpMyAdmin or SQL:
```sql
INSERT INTO Book (BookID, BookTitle, ISBN, Price, Author, Type, Image) 
VALUES ('B-005', 'Your Book Title', '123-456-789-5', 29.99, 'Author Name', 'Fiction', 'image/yourbook.jpg');
```

### Modifying Styles
- Edit `style.css` to customize colors, fonts, and layouts
- CSS variables are defined in `:root` for easy theme customization
- Responsive breakpoints can be adjusted in media queries

### Database Configuration
- Modify `connectDB.php` for different database settings
- Update connection parameters for production deployment

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Failed**
- Check MySQL service is running
- Verify database credentials in `connectDB.php`
- Ensure `bookstore` database exists

**Images Not Loading**
- Check file paths in the `Book` table
- Ensure image files exist in the `image/` folder
- Verify web server has read permissions

**PHP Errors**
- Enable error reporting for development:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

**Session Issues**
- Ensure PHP sessions are enabled
- Check session save path permissions

### Performance Tips
- Enable PHP OPcache for better performance
- Optimize images for web (compress and resize)
- Use a CDN for Font Awesome icons in production
- Consider implementing database connection pooling

## 🔒 Security Notes

- Passwords are stored in plain text (for demo purposes)
- In production, implement password hashing with `password_hash()`
- Add CSRF protection for forms
- Implement rate limiting for login attempts
- Use HTTPS in production
- Sanitize all user inputs
- Regular security updates

## 📱 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Support

For support and questions:
- Check the troubleshooting section above
- Review the code comments for implementation details
- Test with sample data provided in `database.sql`

---

**Happy Coding! 🚀**
