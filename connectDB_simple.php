<?php
/**
 * Simple Database Configuration and Connection
 * Maximum compatibility with all PHP versions and MySQL setups
 */

// Database configuration
$db_host = 'localhost';
$db_port = '3306';
$db_name = 'bookstore';
$db_user = 'root';
$db_pass = '';

// Simple PDO connection function
function getDbConnection() {
    global $db_host, $db_port, $db_name, $db_user, $db_pass;
    
    try {
        $dsn = "mysql:host=" . $db_host . ";dbname=" . $db_name;
        
        // Basic PDO options for maximum compatibility
        $options = array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        );
        
        $pdo = new PDO($dsn, $db_user, $db_pass, $options);
        
        // Set charset using a query (more compatible than DSN charset)
        $pdo->exec("SET NAMES utf8");
        
        return $pdo;
        
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        die("Database connection failed. Please check your database settings.");
    }
}

// Create global PDO connection for backward compatibility
try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die("Could not connect to database: " . $e->getMessage());
}

// Simple database class for those who prefer OOP
class SimpleDatabase {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDbConnection();
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function prepare($sql) {
        return $this->pdo->prepare($sql);
    }
    
    public function query($sql) {
        return $this->pdo->query($sql);
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    public function execute($sql, $params = array()) {
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }
}

// Test connection function
function testConnection() {
    try {
        $pdo = getDbConnection();
        $result = $pdo->query("SELECT 1 as test");
        $row = $result->fetch();
        return ($row['test'] == 1);
    } catch (Exception $e) {
        return false;
    }
}

// Display connection status (for debugging)
function showConnectionStatus() {
    if (testConnection()) {
        echo "<div style='color: green; padding: 10px; background: #f0f8f0; border: 1px solid green; margin: 10px;'>";
        echo "✅ Database connection successful!";
        echo "</div>";
    } else {
        echo "<div style='color: red; padding: 10px; background: #f8f0f0; border: 1px solid red; margin: 10px;'>";
        echo "❌ Database connection failed!";
        echo "</div>";
    }
}
?>
