<?php
/**
 * Alternative Database Configuration and Connection
 * Using array-based configuration for easier management
 */

// Database configuration array
$dbConfig = array(
    'host' => 'localhost',
    'port' => '3306',
    'user' => 'root',
    'password' => '',
    'dbname' => 'bookstore',
    'charset' => 'utf8mb4'
);

class Database {
    private static $instance = null;
    private $pdo;
    private $config;
    
    private function __construct($config) {
        $this->config = $config;
        try {
            $dsn = "mysql:host=" . $config['host'] . 
                   ";port=" . $config['port'] . 
                   ";dbname=" . $config['dbname'] . 
                   ";charset=" . $config['charset'];
                   
            $options = array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . $config['charset']
            );
            
            $this->pdo = new PDO($dsn, $config['user'], $config['password'], $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance($config = null) {
        if (self::$instance === null) {
            if ($config === null) {
                throw new Exception("Database configuration is required for first connection.");
            }
            self::$instance = new self($config);
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function prepare($sql) {
        return $this->pdo->prepare($sql);
    }
    
    public function query($sql) {
        return $this->pdo->query($sql);
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    public function getConfig() {
        return $this->config;
    }
}

// Initialize database connection with configuration
try {
    $db = Database::getInstance($dbConfig);
    $pdo = $db->getConnection();
} catch (Exception $e) {
    error_log("Database initialization failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}

// Simple connection function for backward compatibility
function getDbConnection() {
    global $dbConfig;
    try {
        $dsn = "mysql:host=" . $dbConfig['host'] . 
               ";port=" . $dbConfig['port'] . 
               ";dbname=" . $dbConfig['dbname'] . 
               ";charset=" . $dbConfig['charset'];
               
        $options = array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        );
        
        return new PDO($dsn, $dbConfig['user'], $dbConfig['password'], $options);
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed.");
    }
}

// Test connection function
function testDatabaseConnection() {
    global $dbConfig;
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->query("SELECT 1");
        return array('success' => true, 'message' => 'Connection successful');
    } catch (Exception $e) {
        return array('success' => false, 'message' => $e->getMessage());
    }
}
?>
