body{
    font-family: Arial;
    margin: 0 auto;
}

header {
    background-color: rgb(0,51,102);
    width: 100%;
}
header img {
    margin: 1%;
}
header .hf{
    float: right;
    margin: 1.5%;
}
header .hi{
    background-color: #fff;
    border: none;
    border-radius: 20px;
    text-align: center;
    transition-duration: 0.5s; 
    padding: 8px 30px;
    cursor: pointer;
    color: #000;
    font-weight: bold;
    margin-top: 15%;
}
header .hi:hover{
    background-color: #ccc;
}


table {
    border-collapse: collapse;
}
tr{background-color: #fff;}
th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: rgb(0,51,102);
    color: white;
}
table .btn{
    background-color: #ec7115;
    border: none;
    text-align: center;
    transition-duration: 0.5s; 
    padding: 8px 30px;
    cursor: pointer;
    color: #fff;
    margin-top: 5%;
}
table .btn:hover{
    background-color: #e3e3e3;
    color: #ec7115;
}

.button{
    background-color: rgb(0,51,102);
    border: none;
    border-radius: 20px;
    text-align: center;
    transition-duration: 0.5s; 
    padding: 8px 30px;
    cursor: pointer;
    color: #fff;
    margin-top: 5%;
    font-weight: bold;
}
.button:hover {
    background-color: rgb(102,255,255);
    color: #000;
}
.cbtn{
    background-color: #fff;
    border: none;
    border-radius: 20px;
    text-align: center;
    transition-duration: 0.5s; 
    padding: 8px 30px;
    cursor: pointer;
    color: #000;
    font-weight: bold;
}
.cbtn:hover{
    background-color: #ccc;
}

form{
	margin-top: 2%;
}
input[type=text], input[type=password]{	
	width: 100%;
	padding: 12px;
    border-radius: 3px;
    box-sizing: border-box;
    border: 2px solid #ccc;
    transition: 0.5s;
    outline: none;
}

input[type=text]:focus, input[type=password]:focus {
    border: 2px solid rgb(0,51,102);
}
textarea {
	outline: none;
	border: 2px solid #ccc;
}
textarea:focus {
	border: 2px solid rgb(0,51,102);
}

.container {
	width: 55%;
    border-radius: 5px;
    background-color: #f2f2f2;
    padding: 20px;
    margin: 0 auto;
}