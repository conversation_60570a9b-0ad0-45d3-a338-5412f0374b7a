/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --light-gray: #ecf0f1;
  --dark-gray: #34495e;
  --text-color: #2c3e50;
  --border-color: #bdc3c7;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header Styles */
header {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--dark-gray) 100%
  );
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-link {
  display: inline-block;
  transition: var(--transition);
}

.logo-link:hover {
  transform: scale(1.05);
}

.logo {
  height: 60px;
  width: auto;
  filter: brightness(1.1);
}

header .hf {
  display: flex;
  gap: 1rem;
  align-items: center;
}

header .hi {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 600;
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

header .hi:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

table {
  border-collapse: collapse;
}
tr {
  background-color: #fff;
}
th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: rgb(0, 51, 102);
  color: white;
}
table .btn {
  background-color: #ec7115;
  border: none;
  text-align: center;
  transition-duration: 0.5s;
  padding: 8px 30px;
  cursor: pointer;
  color: #fff;
  margin-top: 5%;
}
table .btn:hover {
  background-color: #e3e3e3;
  color: #ec7115;
}

.button {
  background-color: rgb(0, 51, 102);
  border: none;
  border-radius: 20px;
  text-align: center;
  transition-duration: 0.5s;
  padding: 8px 30px;
  cursor: pointer;
  color: #fff;
  margin-top: 5%;
  font-weight: bold;
}
.button:hover {
  background-color: rgb(102, 255, 255);
  color: #000;
}
.cbtn {
  background-color: #fff;
  border: none;
  border-radius: 20px;
  text-align: center;
  transition-duration: 0.5s;
  padding: 8px 30px;
  cursor: pointer;
  color: #000;
  font-weight: bold;
}
.cbtn:hover {
  background-color: #ccc;
}

form {
  margin-top: 2%;
}
input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 12px;
  border-radius: 3px;
  box-sizing: border-box;
  border: 2px solid #ccc;
  transition: 0.5s;
  outline: none;
}

input[type="text"]:focus,
input[type="password"]:focus {
  border: 2px solid rgb(0, 51, 102);
}
textarea {
  outline: none;
  border: 2px solid #ccc;
}
textarea:focus {
  border: 2px solid rgb(0, 51, 102);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Form Styles */
.login-form-wrapper,
.register-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 3rem;
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.register-form-wrapper {
  max-width: 800px;
}

.form-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-color);
  font-size: 2rem;
  font-weight: 300;
}

.form-title i {
  margin-right: 0.5rem;
  color: var(--secondary-color);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-group label i {
  margin-right: 0.5rem;
  color: var(--secondary-color);
  width: 16px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.address-textarea {
  resize: vertical;
  min-height: 100px;
}

.error-text {
  display: block;
  color: var(--accent-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.error-message {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-message {
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid var(--success-color);
  color: var(--success-color);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Radio Button Styles */
.gender-label {
  margin-bottom: 0.5rem !important;
}

.radio-group {
  display: flex;
  gap: 2rem;
  margin-top: 0.5rem;
}

.radio-label {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  margin-right: 0.5rem;
  position: relative;
  transition: var(--transition);
}

.radio-label input[type="radio"]:checked + .radio-custom {
  border-color: var(--secondary-color);
  background: var(--secondary-color);
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

/* Button Styles */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.button {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  min-width: 140px;
  justify-content: center;
}

.button.primary {
  background: linear-gradient(135deg, var(--secondary-color), #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.button.secondary {
  background: var(--light-gray);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.button.secondary:hover {
  background: var(--border-color);
  transform: translateY(-2px);
}

/* Footer Styles */
.footer {
  background: var(--primary-color);
  color: white;
  text-align: center;
  padding: 2rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Welcome Text */
.welcome-text {
  color: white;
  font-weight: 500;
  margin-right: 1rem;
}

/* Bookstore Header */
.bookstore-header {
  text-align: center;
  margin-bottom: 3rem;
}

.bookstore-header h1 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.bookstore-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Books Grid */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* Book Card */
.book-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.book-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.book-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.book-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.book-card:hover .book-image img {
  transform: scale(1.05);
}

.book-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.book-type {
  background: var(--secondary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.book-info {
  padding: 1.5rem;
}

.book-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.book-author,
.book-isbn {
  color: var(--dark-gray);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.book-author i,
.book-isbn i {
  color: var(--secondary-color);
  width: 16px;
}

.book-price {
  margin: 1rem 0;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
}

.add-to-cart-form {
  margin-top: 1rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.quantity-selector label {
  font-weight: 600;
  color: var(--text-color);
}

.quantity-selector input {
  width: 60px;
  padding: 0.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  text-align: center;
}

.add-to-cart-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--success-color), #229954);
  color: white;
  border: none;
  padding: 12px;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* Cart Section */
.cart-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  margin-top: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--light-gray);
}

.cart-header h2 {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin: 0;
}

.cart-header h2 i {
  color: var(--secondary-color);
  margin-right: 0.5rem;
}

.empty-cart-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.empty-cart-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

.cart-items {
  margin-bottom: 2rem;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  transition: var(--transition);
}

.cart-item:hover {
  box-shadow: var(--shadow);
}

.cart-item-image {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 1rem;
}

.cart-item-details h4 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1rem;
}

.cart-item-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.cart-item-price {
  font-weight: 600;
  color: var(--secondary-color);
}

.cart-item-total {
  font-weight: 600;
  color: var(--accent-color);
}

.empty-cart {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--dark-gray);
}

.empty-cart i {
  font-size: 3rem;
  color: var(--border-color);
  margin-bottom: 1rem;
}

.empty-cart p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.cart-summary {
  border-top: 2px solid var(--light-gray);
  padding-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-total {
  font-size: 1.3rem;
  color: var(--primary-color);
}

.checkout-btn {
  background: linear-gradient(135deg, var(--success-color), #229954);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .login-form-wrapper,
  .register-form-wrapper {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .radio-group {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .button {
    width: 100%;
  }

  .bookstore-header h1 {
    font-size: 2rem;
  }

  .books-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .welcome-text {
    display: none;
  }
}
